// DDLC-Inspired Color Palette
$ui-background-colour: #111;
$ui-text-colour: #ddd;
$ui-highlight-text-colour: #fff;
$ui-muted-text-colour: #aaa;

// DDLC Character Colors
$ddlc-monika: #00d166;
$ddlc-sayori: #22bbff;
$ddlc-natsuki: #ff69b4;
$ddlc-yuri: #a855f7;
$ddlc-mc: #ffaa44;

// Enhanced UI Colors
$ui-primary-colour-bg: #ff69b4; // Natsuki pink
$ui-secondary-colour-bg: #a855f7; // Yuri purple
$ui-dark-colour-bg: #1e1e1e;
$ui-danger-colour-bg: #eb4d4b;
$ui-warning-colour-bg: #f0932b;
$ui-success-colour-bg: #00d166; // Monika green

// DDLC Gradients - Dark Theme Optimized with Enhanced Glass Effects
$ui-highlight-gradient: linear-gradient(315deg, rgba(255, 105, 180, 0.6), rgba(168, 85, 247, 0.4));
$ddlc-cute-gradient: linear-gradient(135deg, rgba(255, 182, 193, 0.3), rgba(255, 160, 200, 0.2));
$ddlc-panel-gradient: linear-gradient(145deg, rgba(30, 30, 30, 0.3), rgba(40, 40, 40, 0.2));
$ddlc-glass-gradient: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
$ddlc-card-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
$ddlc-glow-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
$ddlc-glass-border: rgba(255, 255, 255, 0.3);

// UI Theme Variables
$classic-panel-bg: rgba(0, 0, 0, 0.5);
$classic-border: 2px solid #1e1e1e;

@use "sass:color";
@use "fonts";

// region Overrides
* {
  box-sizing: border-box;
  // Remove any default borders or outlines that might create gray squares
  border: none;
  outline: none;
}

// Remove any potential Electron window chrome styling
::-webkit-scrollbar-corner,
::-webkit-resizer {
  background: transparent;
}

// Ensure webview and any embedded content respects rounded corners
webview {
  border-radius: 12px;
  overflow: hidden;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: "Open Sans", sans-serif;
  user-select: none;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: transparent; // Make sure there's no background behind the app
  // Ensure no default browser styling
  border: none;
  outline: none;
}

body {
  background: transparent; // Remove any background color
  color: $ui-text-colour;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: $ui-background-colour; // Move background to app container
  background-size: cover;
  border-radius: 12px;
  position: relative;
  // Use clip-path to create proper rounded corners that hide content
  clip-path: inset(0 round 12px);
  // Ensure no border or outline
  border: none;
  outline: none;
  box-shadow: none;
}

// Remove any potential gray backgrounds from common container elements
.container, .main-container, .app-container, .content, .wrapper {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

// Specifically target any elements that might have gray backgrounds
div, section, main, article {
  &:not(.mod-viewer-mod-list):not(.mod-viewer-mod-display):not(.text-container) {
    background: transparent;
  }
}

// Allow scrolling in text containers
.text-container {
  overflow-y: auto !important;
}

::-webkit-scrollbar {
  background-color: transparent;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 3px;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

// Global fix for blur effects during scrolling
[style*="backdrop-filter"],
*[class*="blur"],
.mod-viewer-mod-list,
.mod-viewer-mod-display,
.mod-view-mod-list-entry,
.theme-option {
  // Ensure proper GPU acceleration for blur effects
  transform: translateZ(0);
  // Optimize rendering during scroll
  will-change: auto;
  // Improve containment for better performance
  contain: layout style;
}

// Specific fix for scrollable containers with blur
.mod-viewer-mod-list,
.mod-viewer-mod-display {
  // Ensure smooth scrolling with blur effects
  scroll-behavior: smooth;
  // Optimize scroll performance
  -webkit-overflow-scrolling: touch;
  // Create a new stacking context to contain blur effects
  position: relative;
  z-index: 0;
  // Ensure proper clipping of blur backgrounds
  overflow: hidden;
}

// Additional fix for blur background sizing during scroll
.mod-viewer-mod-list,
.mod-viewer-mod-display,
.ddlc-options-container .mod-viewer-mod-list,
.ddlc-options-container .mod-viewer-mod-display {
  // Override overflow to ensure proper blur containment
  overflow-y: auto !important;
  // Ensure blur backgrounds are properly contained
  &::before {
    // Force the background to stay within bounds
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    // Ensure it covers the full scrollable area
    height: 100% !important;
    width: 100% !important;
  }
}

// endregion

// region Navbar / titlebar
.titlebar {
  color: $ui-highlight-text-colour;
  -webkit-app-region: drag;
  text-align: right;
  padding: 0.5em;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  // Add padding to prevent content from going into rounded corners
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 12px;
}

.app-title {
  padding: 0.5em;
  font-size: 1.25em;
}

// macOS adjust title position
.os-darwin .app-title {
  padding-left: 100px;
}

.navbar {
  color: $ui-highlight-text-colour;
  display: flex;
  flex-shrink: 0;
  // Add padding to prevent content from going into rounded corners
  padding-left: 12px;
  padding-right: 12px;
}

.navbar > div {
  padding: 0.5em;
}

.navbar img {
  vertical-align: middle;
}

.nav-links {
  display: flex;
  height: 100%;
  font-size: 1.25em;
  flex-grow: 1;
}

.nav-links div {
  height: 100%;
  padding: 0.5em;
  -webkit-app-region: no-drag;
  color: $ui-muted-text-colour;
  transition: color 0.25s, text-shadow 0.25s;
}

.nav-links div:hover {
  color: $ui-highlight-text-colour;
}

.nav-links div.active {
  color: $ui-highlight-text-colour;
  text-shadow: 0 0 10px #fff;
}

// macOS hide buttons
.os-darwin .window-buttons {
  visibility: hidden;
}

.window-buttons {
  position: fixed;
  z-index: 200;
  right: 0;
  top: 0;
  display: flex;
  color: $ui-text-colour;
  font-weight: bold;
  padding: 0.5em;
  -webkit-app-region: no-drag;
}

.window-buttons div {
  padding: 0.5em;
  transition: color 0.25s;
}

.window-buttons div:hover {
  color: $ui-highlight-text-colour;
}

@keyframes draw-attention {
  from {
    color: white;
  }

  to {
    color: red;
  }
}

.window-button-draw-attention {
  animation: draw-attention 1s infinite alternate;
}

// endregion

// region Content
#app {
  display: flex;
  flex-direction: column;
}

.page-content {
  height: calc(100% - 63px - 67px);
  opacity: 1;
  transform: translateX(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

// Page transition animations
.page-transition-enter {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-leave {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-leave-active {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Slide animation variants
.slide-left-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-left-enter-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left-leave-active {
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-enter {
  opacity: 0;
  transform: translateX(100%);
}

.slide-right-enter-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right-leave-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

// Fade animation
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  transition: opacity 0.4s ease;
}

.fade-leave-active {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.text-container {
  padding: 2em;
  height: calc(100vh - 130px - 4em); // Fixed height for proper scrolling
  overflow-y: auto;
  background: $ddlc-panel-gradient;
  border-radius: 20px;
  margin: 1em;
  box-shadow: $ddlc-card-shadow, inset 0 1px 0 $ddlc-glass-border;
  border: 1px solid $ddlc-glass-border;
  backdrop-filter: blur(25px);
  color: $ui-text-colour;
  position: relative;
}

.text-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $ddlc-glass-gradient;
  border-radius: 20px;
  pointer-events: none;
  z-index: -1;
}

h1, h2, h3, h4 {
  margin: 0;
  font-weight: 500;
}

small {
  font-size: 0.6em;
}

p {
  margin: 0;
}

a {
  color: $ui-text-colour;
  text-decoration: none;
  font-weight: bold;
  -webkit-app-region: no-drag;
  outline: none;
}

a .fab {
  font-weight: normal;
}

.tag {
  font-size: 0.5em;
  padding: 0.1em 0.25em;
  background-color: $ui-text-colour;
  color: #000;
  border-radius: 3px;
}

// endregion

// region DDLC-Themed Mod List
.mod-viewer-pane {
  display: flex;
  height: 100%;
  flex-grow: 1;
  gap: 1em;
  padding: 1em;
  // Add extra padding to prevent content from going into rounded corners
  padding-left: 1.5em;
  padding-right: 1.5em;
  padding-bottom: 1.5em;
}

.mod-viewer-mod-list {
  width: 28%;
  max-height: 100%;
  background: $ddlc-panel-gradient;
  border-radius: 20px;
  padding: 1.2em;
  overflow-y: auto;
  box-shadow: $ddlc-card-shadow, inset 0 1px 0 $ddlc-glass-border;
  border: 1px solid $ddlc-glass-border;
  backdrop-filter: blur(25px);
  position: relative;
  // Ensure proper scrolling behavior with blur effects
  contain: layout style paint;
  // Fix for blur background sizing during scroll
  isolation: isolate;
  // Use background instead of ::before for better scroll behavior
  background-image: $ddlc-glass-gradient, $ddlc-panel-gradient;
  background-attachment: local, local;
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat, no-repeat;
}

// Remove the ::before pseudo-element as we're using background-image instead
.mod-viewer-mod-list::before {
  display: none;
}

.mod-viewer-mod-display {
  width: 72%;
  background: $ddlc-panel-gradient;
  border-radius: 20px;
  padding: 1.5em;
  overflow-y: auto;
  box-shadow: $ddlc-card-shadow, inset 0 1px 0 $ddlc-glass-border;
  border: 1px solid $ddlc-glass-border;
  backdrop-filter: blur(25px);
  position: relative;
  // Ensure proper scrolling behavior with blur effects
  contain: layout style paint;
  // Fix for blur background sizing during scroll
  isolation: isolate;
  // Use background instead of ::before for better scroll behavior
  background-image: $ddlc-glass-gradient, $ddlc-panel-gradient;
  background-attachment: local, local;
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat, no-repeat;
}

// Remove the ::before pseudo-element as we're using background-image instead
.mod-viewer-mod-display::before {
  display: none;
}

.mod-view-mod-list-title {
  padding: 0.8em 0.5em 0.4em;
  font-weight: 700;
  text-transform: uppercase;
  color: $ddlc-natsuki;
  font-size: 0.9em;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(255, 105, 180, 0.3);
  border-bottom: 2px solid rgba(255, 105, 180, 0.3);
  margin-bottom: 0.5em;
  position: relative;
}

.mod-view-mod-list-title::after {
  content: '♡';
  position: absolute;
  right: 0.5em;
  top: 50%;
  transform: translateY(-50%);
  color: $ddlc-natsuki;
  font-size: 1.2em;
}

.mod-view-mod-list-entry {
  padding: 0.8em 1em;
  width: 100%;
  border-radius: 12px;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
  justify-content: space-between;
  display: flex;
  margin-bottom: 0.4em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: $ui-text-colour;
  font-weight: 500;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  // Improve performance for blur effects during scrolling
  contain: layout style;
  transform: translateZ(0);
}

.mod-view-mod-list-entry::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $ddlc-glass-gradient;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
  // Ensure proper rendering during scroll
  transform: translateZ(0);
  will-change: opacity;
}

.mod-view-mod-list-entry:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 105, 180, 0.4);
  box-shadow: 0 4px 16px rgba(255, 105, 180, 0.2);
  color: $ui-highlight-text-colour;
}

.mod-view-mod-list-entry:hover::before {
  opacity: 1;
}

.mod-view-mod-list-entry span {
  transition: color 0.25s;
}

.mod-view-mod-list-entry span:hover {
  color: $ddlc-natsuki;
}

.mod-view-mod-list-entry span:not(.mod-view-mod-list-entry-button) {
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.mod-view-mod-list-entry-button {
  flex-grow: 0;
  flex-shrink: 0;
  display: none;
  color: $ddlc-natsuki;
  font-weight: 600;
  padding: 0.2em 0.4em;
  margin-left: 0.3em;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.mod-view-mod-list-entry-button:hover {
  background: rgba(255, 105, 180, 0.2);
  transform: scale(1.1);
}

.mod-view-mod-list-entry-buttons {
  display: flex;
  align-items: center;
  gap: 0.2em;
}

.mod-view-mod-list-entry.active .mod-view-mod-list-entry-button,
.mod-view-mod-list-entry:hover .mod-view-mod-list-entry-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.edit-button {
  color: $ddlc-sayori !important;
}

.edit-button:hover {
  background: rgba(34, 187, 255, 0.2) !important;
}

.mod-view-mod-list-entry.disabled {
  color: $ui-muted-text-colour !important;
  opacity: 0.6;
  cursor: not-allowed;
}

.mod-view-mod-list-entry.disabled:hover {
  transform: none;
  box-shadow: none;
}

.mod-view-mod-list-entry.active {
  background: linear-gradient(135deg, $ddlc-natsuki, rgba(255, 105, 180, 0.8));
  color: white;
  border-color: $ddlc-natsuki;
  box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
  transform: translateY(-2px);
}

.mod-view-mod-list-entry.active span {
  color: white;
}

.screenshots {
  max-width: 100%;
  overflow-x: scroll;
  display: flex;
}

.screenshots img {
  flex-shrink: 0;
  flex-grow: 0;
  display: block;
  width: 192px;
  height: 108px;
  padding: 0.5em;
  cursor: pointer;
  -webkit-user-drag: none;
}

// endregion

// region DDLC-Themed Buttons

button {
  font-family: "Open Sans", sans-serif;
  color: white;
  user-select: none;
  display: inline-block;
  text-decoration: none;
  padding: 0.8em 2.2em;
  font-size: 14px;
  border-radius: 25px;
  text-align: center;
  font-weight: 600;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button.huge {
  font-size: 1.25em;
  padding: 1em 2.5em;
}

button.primary {
  background: linear-gradient(135deg, $ddlc-natsuki, rgba(255, 105, 180, 0.8));
  border-color: $ddlc-natsuki;
  box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
}

button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
  background: linear-gradient(135deg, color.adjust($ddlc-natsuki, $lightness: 5%), $ddlc-natsuki);
}

button.secondary {
  background: linear-gradient(135deg, $ddlc-yuri, rgba(168, 85, 247, 0.8));
  border-color: $ddlc-yuri;
  box-shadow: 0 4px 15px rgba(168, 85, 247, 0.3);
}

button.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(168, 85, 247, 0.4);
  background: linear-gradient(135deg, color.adjust($ddlc-yuri, $lightness: 5%), $ddlc-yuri);
}

button.dark {
  background: linear-gradient(135deg, $ui-dark-colour-bg, rgba(30, 30, 30, 0.9));
  border-color: rgba(255, 255, 255, 0.2);
  color: $ui-text-colour;
}

button.dark:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, color.adjust($ui-dark-colour-bg, $lightness: 10%), $ui-dark-colour-bg);
  border-color: rgba(255, 255, 255, 0.3);
}

button.danger {
  background: linear-gradient(135deg, $ui-danger-colour-bg, rgba(235, 77, 75, 0.8));
  border-color: $ui-danger-colour-bg;
  box-shadow: 0 4px 15px rgba(235, 77, 75, 0.3);
}

button.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(235, 77, 75, 0.4);
  background: linear-gradient(135deg, color.adjust($ui-danger-colour-bg, $lightness: 5%), $ui-danger-colour-bg);
}

button.warning {
  background: linear-gradient(135deg, $ui-warning-colour-bg, rgba(240, 147, 43, 0.8));
  border-color: $ui-warning-colour-bg;
  box-shadow: 0 4px 15px rgba(240, 147, 43, 0.3);
}

button.warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(240, 147, 43, 0.4);
  background: linear-gradient(135deg, color.adjust($ui-warning-colour-bg, $lightness: 5%), $ui-warning-colour-bg);
}

button.success {
  background: linear-gradient(135deg, $ddlc-monika, rgba(0, 209, 102, 0.8));
  border-color: $ddlc-monika;
  box-shadow: 0 4px 15px rgba(0, 209, 102, 0.3);
}

button.success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 209, 102, 0.4);
  background: linear-gradient(135deg, color.adjust($ddlc-monika, $lightness: 5%), $ddlc-monika);
}

button[disabled], button[disabled]:hover {
  background: linear-gradient(135deg, $ui-muted-text-colour, rgba(170, 170, 170, 0.8));
  color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

// endregion

// region DDLC-Themed Inputs
.form-group {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

input[type="text"], input[type="number"], input[type="url"], textarea, select {
  font-family: "Open Sans", sans-serif;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid $ddlc-glass-border;
  color: $ui-text-colour;
  font-weight: 500;
  min-width: 350px;
  padding: 0.8em 1.2em;
  font-size: 1em;
  width: 100%;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

input.small {
  font-size: 0.85em;
  min-width: 0;
  padding: 0.6em 1em;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
}

input:focus, textarea:focus, select:focus {
  border-color: $ddlc-natsuki;
  box-shadow: 0 0 0 3px rgba(255, 105, 180, 0.2), 0 4px 12px rgba(255, 105, 180, 0.15);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

input:hover, textarea:hover, select:hover {
  border-color: rgba(255, 105, 180, 0.5);
  box-shadow: 0 4px 12px rgba(255, 105, 180, 0.15);
  background: rgba(255, 255, 255, 0.15);
}

input::placeholder, textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

// Enhanced Checkboxes
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid $ddlc-glass-border;
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  min-width: 20px;
}

input[type="checkbox"]:hover {
  border-color: rgba(255, 105, 180, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px rgba(255, 105, 180, 0.2);
}

input[type="checkbox"]:checked {
  background: linear-gradient(135deg, $ddlc-natsuki, rgba(255, 105, 180, 0.8));
  border-color: $ddlc-natsuki;
  box-shadow: 0 0 15px rgba(255, 105, 180, 0.4);
}

input[type="checkbox"]:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 105, 180, 0.3);
}

// Radio buttons
input[type="radio"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid $ddlc-glass-border;
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  min-width: 20px;
}

input[type="radio"]:hover {
  border-color: rgba(255, 105, 180, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 10px rgba(255, 105, 180, 0.2);
}

input[type="radio"]:checked {
  background: linear-gradient(135deg, $ddlc-natsuki, rgba(255, 105, 180, 0.8));
  border-color: $ddlc-natsuki;
  box-shadow: 0 0 15px rgba(255, 105, 180, 0.4);
}

input[type="radio"]:checked::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

input[type="radio"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 105, 180, 0.3);
}

// endregion

// region running and crash covers
.cover {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background-size: cover;
  color: $ui-text-colour;
  padding: 3em;
}

.cover.crash {
  z-index: 200;
}

.cover h1 {
  font-size: 2.25em;
}

.cover p, .cover pre {
  font-size: 1.25em;
}

// endregion

// region progress bar
.progress {
  width: 100%;
  padding: 0;
}

.progress .bar {
  min-width: 1%;
  padding: 0;
  margin: 0;
  height: 16px;
  background-image: $ui-highlight-gradient;
  transition: width 0.5s;
  border-radius: 3px;
}

// endregion

// region Drag and drop overlay
.drop-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9001;
  padding: 1em;
}

.drop-overlay .info {
  padding: 5em;
  font-size: 2em;
  text-align: center;
  height: 100%;
}

.drop-overlay * {
  pointer-events: none;
}

// endregion

.is-appx .hide-appx {
  display: none;
}

// region Enhanced Onboarding Styles
.onboarding-overlay {
  background: linear-gradient(135deg, rgba(17, 17, 17, 0.95), rgba(30, 30, 30, 0.95));
  backdrop-filter: blur(10px);
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  padding: 0;
  height: 100vh;
  width: 100vw;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
}

.onboarding-container {
  width: 100%;
  height: 100%;
  background: rgba(30, 30, 30, 0.95);
  padding: 1.5em 2em;
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  gap: 1em;
  overflow: hidden;
  box-sizing: border-box;
}

.onboarding-header {
  text-align: center;
  background: rgba(40, 40, 40, 0.8);
  padding: 1em;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.onboarding-header h1 {
  font-size: 1.6em;
  margin-bottom: 0.2em;
  background: linear-gradient(135deg, $ui-primary-colour-bg, color.adjust($ui-primary-colour-bg, $lightness: 20%));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.onboarding-subtitle {
  font-size: 0.9em;
  color: $ui-text-colour;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.onboarding-progress {
  background: rgba(40, 40, 40, 0.6);
  padding: 1em;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8em;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3em;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.progress-step.active {
  opacity: 1;
}

.progress-step.completed {
  opacity: 1;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: $ui-dark-colour-bg;
  color: $ui-muted-text-colour;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9em;
  border: 2px solid $ui-dark-colour-bg;
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: $ui-primary-colour-bg;
  color: white;
  border-color: $ui-primary-colour-bg;
  box-shadow: 0 0 15px rgba(187, 85, 153, 0.4);
}

.progress-step.completed .step-number {
  background: $ui-success-colour-bg;
  color: white;
  border-color: $ui-success-colour-bg;
}

.step-label {
  font-size: 0.7em;
  color: $ui-muted-text-colour;
  text-align: center;
  transition: color 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.progress-step.active .step-label {
  color: $ui-highlight-text-colour;
  font-weight: 500;
}

.progress-line {
  width: 30px;
  height: 2px;
  background: $ui-dark-colour-bg;
  transition: background 0.3s ease;
  border-radius: 1px;
}

.progress-line.active {
  background: $ui-primary-colour-bg;
}

.onboarding-content {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  padding: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}

.onboarding-step {
  text-align: center;
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1em;
}

.step-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: $ui-primary-colour-bg;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3em;
  margin: 0 auto;
  box-shadow: 0 6px 15px rgba(187, 85, 153, 0.3);
  flex-shrink: 0;
}

.step-icon.success {
  background: $ui-success-colour-bg;
  box-shadow: 0 6px 15px rgba(106, 176, 76, 0.3);
}

.step-icon.character {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  box-shadow: 0 6px 15px rgba(255, 105, 180, 0.4);
}

.onboarding-step h2 {
  font-size: 1.4em;
  margin: 0;
  color: $ui-highlight-text-colour;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.step-description {
  font-size: 0.95em;
  color: $ui-text-colour;
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.step-details {
  margin: 0;
}

// Character Selection Styles - DDLC Inspired
.character-selection {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.character-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1em;
  margin: 1em 0;
  flex: 1;
  align-items: center;
  max-height: none;
}

.character-option {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(240, 240, 240, 0.9));
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 1.2em 1em;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  min-height: 140px;
  justify-content: center;
}

.character-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 182, 193, 0.3) 0%,
    rgba(255, 160, 200, 0.2) 25%,
    rgba(200, 180, 255, 0.2) 50%,
    rgba(180, 200, 255, 0.2) 75%,
    rgba(255, 200, 180, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.character-option:hover {
  transform: translateY(-4px) scale(1.01);
  border-color: rgba(255, 105, 180, 0.6);
  box-shadow: 0 8px 24px rgba(255, 105, 180, 0.25);
}

.character-option:hover::before {
  opacity: 1;
}

.character-option.selected {
  border-color: #ff69b4;
  background: linear-gradient(145deg, rgba(255, 255, 255, 1), rgba(255, 240, 245, 0.95));
  box-shadow: 0 0 20px rgba(255, 105, 180, 0.4), 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px) scale(1.02);
}

.character-option.selected::before {
  opacity: 0.7;
}

.character-radio {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(255, 105, 180, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.8em;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.character-option.selected .character-radio {
  border-color: #ff69b4;
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 105, 180, 0.4);
}

.character-radio i {
  font-size: 1em;
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.character-name {
  font-size: 1.1em;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.3em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-family: 'Open Sans', sans-serif;
  letter-spacing: 0.3px;
}

.character-option.selected .character-name {
  color: #d63384;
  text-shadow: 0 1px 4px rgba(214, 51, 132, 0.3);
}

.character-description {
  font-size: 0.85em;
  color: #6c757d;
  line-height: 1.3;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

// Responsive design for character selection
@media (max-width: 1200px) {
  .character-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8em;
  }

  .character-option {
    min-height: 120px;
    padding: 1em 0.8em;
  }

  .character-name {
    font-size: 1em;
  }

  .character-description {
    font-size: 0.8em;
  }
}

@media (max-width: 900px) {
  .character-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8em;
  }

  .character-option {
    min-height: 110px;
    padding: 1em 0.8em;
  }

  .character-name {
    font-size: 1em;
  }

  .character-description {
    font-size: 0.8em;
  }
}

@media (max-width: 600px) {
  .character-list {
    grid-template-columns: 1fr;
    gap: 0.6em;
    margin: 0.8em 0;
  }

  .character-option {
    min-height: 80px;
    padding: 0.8em;
    flex-direction: row;
    text-align: left;
  }

  .character-radio {
    margin-bottom: 0;
    margin-right: 0.8em;
    width: 20px;
    height: 20px;
  }

  .character-info {
    text-align: left;
  }

  .character-name {
    font-size: 1em;
    margin-bottom: 0.2em;
  }

  .character-description {
    font-size: 0.8em;
  }
}

.info-box {
  background: rgba(187, 85, 153, 0.15);
  border: 1px solid rgba(187, 85, 153, 0.4);
  border-radius: 8px;
  padding: 1.2em;
  display: flex;
  align-items: flex-start;
  gap: 0.8em;
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
  backdrop-filter: blur(5px);
}

.info-box.success {
  background: rgba(106, 176, 76, 0.15);
  border-color: rgba(106, 176, 76, 0.4);
}

.info-box i {
  color: $ui-primary-colour-bg;
  font-size: 1.2em;
  margin-top: 0.1em;
  flex-shrink: 0;
}

.info-box.success i {
  color: $ui-success-colour-bg;
}

.info-box strong {
  color: $ui-highlight-text-colour;
  display: block;
  margin-bottom: 0.3em;
  font-size: 1em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.info-box p {
  color: $ui-text-colour;
  margin: 0;
  line-height: 1.4;
  font-size: 0.9em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.step-actions {
  display: flex;
  gap: 0.8em;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 0.8em;
}

.step-actions button {
  min-width: 120px;
  padding: 0.6em 1.2em;
  font-size: 0.9em;
  border-radius: 6px;
  font-weight: 600;
}

.onboarding-footer {
  background: rgba(40, 40, 40, 0.6);
  padding: 1em;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.storage-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
  color: $ui-text-colour;
  font-size: 0.8em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.storage-info i {
  color: $ui-primary-colour-bg;
  font-size: 0.9em;
}

.change-link {
  color: $ui-primary-colour-bg;
  font-weight: 500;
  text-decoration: underline;
  margin-left: 0.5em;
  font-size: 0.8em;
  transition: color 0.3s ease;
}

.change-link:hover {
  color: color.adjust($ui-primary-colour-bg, $lightness: 15%);
}

// endregion

// region Sayonika Server Configuration
.button-group {
  display: flex;
  gap: 0.5em;
  flex-wrap: wrap;
}

.button-group button {
  margin: 0;
}

.test-result {
  padding: 0.75em 1em;
  border-radius: 3px;
  margin: 1em 0;
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.test-result.success {
  background-color: rgba(106, 176, 76, 0.2);
  border: 1px solid $ui-success-colour-bg;
  color: $ui-success-colour-bg;
}

.test-result.error {
  background-color: rgba(235, 77, 75, 0.2);
  border: 1px solid $ui-danger-colour-bg;
  color: $ui-danger-colour-bg;
}

.info-box {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  padding: 1em;
  margin: 1em 0;
}

.info-box h3 {
  margin-top: 0;
  margin-bottom: 0.5em;
  color: $ui-highlight-text-colour;
}

.info-box ul {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.info-box li {
  margin: 0.25em 0;
}

.info-box p {
  margin: 0.5em 0 0 0;
  color: $ui-muted-text-colour;
}

// endregion

// region DDLC-Themed Menu Enhancements

// DDLC-Themed Options Menu
.ddlc-options-container {
  // Ensure no gray boxes appear during scrolling
  overflow: hidden;
  // Improve scrolling performance and blur rendering
  contain: layout style paint;

  // Remove any potential gray backgrounds from all child elements
  * {
    &:not(.mod-viewer-mod-list):not(.mod-viewer-mod-display):not(.theme-option):not(.theme-preview) {
      background: transparent !important;
    }
  }
  .mod-viewer-mod-list {
    background: transparent;
    border: 2px solid rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
    backdrop-filter: blur(25px);
    // Improve scrolling performance with blur effects
    contain: layout style paint;
    transform: translateZ(0);
    will-change: scroll-position;
    // Use background instead of ::before for better scroll behavior
    background-image: $ddlc-glass-gradient;
    background-attachment: local;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    // Ensure scrollbar styling doesn't create gray boxes
    ::-webkit-scrollbar {
      background: transparent;
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(168, 85, 247, 0.3);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-corner {
      background: transparent;
    }
  }

  .mod-viewer-mod-display {
    background: transparent;
    border: 2px solid rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
    backdrop-filter: blur(25px);
    // Improve scrolling performance with blur effects
    contain: layout style paint;
    transform: translateZ(0);
    will-change: scroll-position;
    // Use background instead of ::before for better scroll behavior
    background-image: $ddlc-glass-gradient;
    background-attachment: local;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    // Ensure scrollbar styling doesn't create gray boxes
    ::-webkit-scrollbar {
      background: transparent;
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(168, 85, 247, 0.3);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-corner {
      background: transparent;
    }
  }

  .mod-view-mod-list-title {
    color: $ddlc-yuri;
    text-shadow: 0 2px 4px rgba(168, 85, 247, 0.3);
    border-bottom-color: rgba(168, 85, 247, 0.3);
  }

  .mod-view-mod-list-title::after {
    content: '✦';
    color: $ddlc-yuri;
  }

  .mod-view-mod-list-entry {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(168, 85, 247, 0.2);
    color: $ui-text-colour;
  }

  .mod-view-mod-list-entry:hover {
    border-color: rgba(168, 85, 247, 0.5);
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.2);
    background: rgba(255, 255, 255, 0.15);
    color: $ui-highlight-text-colour;
  }

  .mod-view-mod-list-entry.active {
    background: linear-gradient(135deg, $ddlc-yuri, rgba(168, 85, 247, 0.8));
    border-color: $ddlc-yuri;
    box-shadow: 0 6px 20px rgba(168, 85, 247, 0.4);
    color: white;
  }


}

// DDLC-Themed About Menu
.ddlc-about-container {
  .text-container {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.9));
    border: 2px solid rgba(0, 209, 102, 0.3);
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 209, 102, 0.1);
  }

  .text-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 209, 102, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  h1 {
    color: $ddlc-monika;
    text-shadow: 0 2px 4px rgba(0, 209, 102, 0.3);
    font-size: 2.2em;
    margin-bottom: 0.5em;
    position: relative;
  }

  h1::after {
    content: '♪';
    position: absolute;
    right: -0.8em;
    top: 0.1em;
    color: $ddlc-monika;
    font-size: 0.8em;
    animation: bounce 2s infinite;
  }

  h2 {
    color: $ddlc-monika;
    border-bottom: 2px solid rgba(0, 209, 102, 0.3);
    padding-bottom: 0.3em;
    margin-bottom: 0.8em;
    margin-top: 1.5em;
  }

  p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1em;
  }

  button.primary {
    background: linear-gradient(135deg, $ddlc-monika, rgba(0, 209, 102, 0.8));
    border-color: $ddlc-monika;
    box-shadow: 0 4px 15px rgba(0, 209, 102, 0.3);
  }

  button.primary:hover {
    box-shadow: 0 6px 20px rgba(0, 209, 102, 0.4);
    background: linear-gradient(135deg, color.adjust($ddlc-monika, $lightness: 5%), $ddlc-monika);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

// DDLC Character-themed mod cards
.ddlc-mod-card {
  background: $ddlc-panel-gradient;
  border-radius: 16px;
  padding: 1.5em;
  margin-bottom: 1em;
  border: 2px solid rgba(255, 105, 180, 0.2);
  box-shadow: $ddlc-card-shadow;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.ddlc-mod-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, $ddlc-natsuki, $ddlc-yuri, $ddlc-sayori, $ddlc-monika);
}

.ddlc-mod-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(255, 105, 180, 0.2);
  border-color: rgba(255, 105, 180, 0.4);
}

.ddlc-mod-card h3 {
  color: $ddlc-natsuki;
  margin-bottom: 0.8em;
  font-size: 1.3em;
  text-shadow: 0 1px 2px rgba(255, 105, 180, 0.3);
}

.ddlc-mod-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 1em;
}

// Enhanced headings for DDLC theme
h1, h2, h3, h4 {
  color: $ui-text-colour;
  font-weight: 600;
  position: relative;
}

h1 {
  font-size: 2em;
  margin-bottom: 0.8em;
  color: $ddlc-natsuki;
  text-shadow: 0 2px 4px rgba(255, 105, 180, 0.3);
}

h2 {
  font-size: 1.5em;
  margin-bottom: 0.6em;
  color: $ddlc-yuri;
  text-shadow: 0 1px 3px rgba(168, 85, 247, 0.3);
}

// About page specific text colors
.ddlc-about-container {
  p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1em;
  }
}

// DDLC-specific component styles
.ddlc-search-container {
  position: relative;
  margin-bottom: 1em;
}

.ddlc-search {
  padding-right: 2.5em !important;
}

.ddlc-search-icon {
  position: absolute;
  right: 1em;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 105, 180, 0.6);
  pointer-events: none;
}

.ddlc-create-entry {
  background: linear-gradient(135deg, rgba(34, 187, 255, 0.1), rgba(255, 105, 180, 0.1)) !important;
  border-color: rgba(34, 187, 255, 0.3) !important;
}

.ddlc-create-entry:hover {
  border-color: rgba(34, 187, 255, 0.6) !important;
  box-shadow: 0 4px 16px rgba(34, 187, 255, 0.2) !important;
}

.ddlc-create-entry.active {
  background: linear-gradient(135deg, $ddlc-sayori, rgba(34, 187, 255, 0.8)) !important;
  border-color: $ddlc-sayori !important;
  box-shadow: 0 6px 20px rgba(34, 187, 255, 0.4) !important;
}

// About tab specific styles
.ddlc-about-header {
  display: flex;
  align-items: center;
  gap: 2em;
  margin-bottom: 2em;
  padding-bottom: 1.5em;
  border-bottom: 2px solid rgba(0, 209, 102, 0.3);
}

.ddlc-logo {
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 209, 102, 0.2);
  transition: transform 0.3s ease;
}

.ddlc-logo:hover {
  transform: scale(1.05) rotate(2deg);
}

.ddlc-title-section {
  flex: 1;
}

.ddlc-subtitle {
  font-size: 1.1em;
  color: rgba(255, 255, 255, 0.8);
  margin: 0.5em 0 0 0;
  font-style: italic;
}

.ddlc-version-info {
  display: flex;
  flex-direction: column;
  gap: 1em;
  margin-top: 1em;
}

.version-item {
  display: flex;
  align-items: center;
  gap: 0.8em;
  padding: 0.8em;
  background: rgba(0, 209, 102, 0.1);
  border-radius: 8px;
  border-left: 4px solid $ddlc-monika;
}

.version-item i {
  color: $ddlc-monika;
  width: 1.2em;
}

.version-badge {
  background: linear-gradient(135deg, $ddlc-monika, rgba(0, 209, 102, 0.8));
  color: white;
  padding: 0.3em 0.8em;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9em;
  box-shadow: 0 2px 8px rgba(0, 209, 102, 0.3);
}

.ddlc-links-section {
  margin-top: 1em;
}

.ddlc-link-button {
  min-width: 200px;
  font-size: 1.1em;
  padding: 1em 2em;
}

// Responsive design for about page
@media (max-width: 768px) {
  .ddlc-about-header {
    flex-direction: column;
    text-align: center;
    gap: 1em;
  }

  .ddlc-logo {
    width: 120px;
  }

  .version-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5em;
  }
}

// Theme Selector Styles
.ddlc-theme-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5em;
  margin-top: 2em;
}

.theme-preview {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1em;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  .preview-header {
    height: 20px;
    width: 100%;
  }

  .preview-content {
    display: flex;
    height: 60px;

    .preview-sidebar {
      width: 30%;
      height: 100%;
    }

    .preview-main {
      width: 70%;
      height: 100%;
    }
  }
}

.theme-option {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid $ddlc-glass-border;
  border-radius: 16px;
  padding: 1.5em;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  // Improve performance for blur effects during scrolling
  contain: layout style;
  transform: translateZ(0);
  will-change: transform;
}

.theme-option:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 105, 180, 0.5);
  box-shadow: 0 8px 24px rgba(255, 105, 180, 0.2);
}

.theme-option.active {
  border-color: $ddlc-natsuki;
  background: rgba(255, 105, 180, 0.15);
  box-shadow: 0 8px 24px rgba(255, 105, 180, 0.3);
}

.theme-option.active::before {
  content: '✓';
  position: absolute;
  top: 1em;
  right: 1em;
  background: $ddlc-natsuki;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

// Enhanced Theme Previews with Live UI Elements
.theme-preview {
  width: 100%;
  height: 160px;
  border-radius: 12px;
  margin-bottom: 1.5em;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-preview:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.5);
}

// Preview Structure
.preview-titlebar {
  height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
  font-size: 10px;
  font-weight: bold;
}

.preview-title {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-controls {
  display: flex;
  gap: 4px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6px;
    font-weight: bold;
  }
}

.preview-navbar {
  height: 20px;
  display: flex;
  font-size: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-nav-item {
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s;

  &.active {
    font-weight: bold;
  }
}

.preview-content {
  display: flex;
  height: 116px;
}

.preview-sidebar {
  width: 35%;
  padding: 6px;
  font-size: 7px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-main {
  width: 65%;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-mod-entry {
  padding: 2px 4px;
  margin: 1px 0;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.active {
    font-weight: bold;
  }
}

.preview-mod-info {
  text-align: center;

  h4 {
    font-size: 9px;
    margin: 0 0 4px 0;
    font-weight: bold;
  }
}

.preview-button {
  display: inline-block;
  padding: 2px 6px;
  margin: 1px 2px;
  border-radius: 3px;
  font-size: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;

  &.primary {
    opacity: 1;
  }

  &.secondary {
    opacity: 0.8;
  }
}

.ddlc-preview {
  background: linear-gradient(145deg, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.8));
  border: 1px solid rgba(255, 105, 180, 0.3);
}

.classic-preview {
  background: #111;
  border: 2px solid #1e1e1e;
}

.preview-header {
  height: 20px;
  margin-bottom: 8px;
}

.ddlc-preview .preview-header {
  background: linear-gradient(90deg, $ddlc-natsuki, $ddlc-yuri, $ddlc-sayori, $ddlc-monika);
}

.classic-preview .preview-header {
  background: #1e1e1e;
}

.preview-content {
  display: flex;
  height: calc(100% - 28px);
  gap: 8px;
  padding: 0 8px;
}

.preview-sidebar {
  width: 30%;
  border-radius: 4px;
}

.preview-main {
  width: 70%;
  border-radius: 4px;
}

.ddlc-preview .preview-sidebar {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ddlc-preview .preview-main {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.classic-preview .preview-sidebar {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid #1e1e1e;
}

.classic-preview .preview-main {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid #1e1e1e;
}

.theme-option h3 {
  color: $ui-text-colour;
  margin-bottom: 0.5em;
  font-size: 1.2em;
}

.theme-option p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1em;
  line-height: 1.4;
}

.theme-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
}

.feature {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3em 0.8em;
  border-radius: 12px;
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature i {
  margin-right: 0.3em;
  color: $ddlc-natsuki;
}

// Enhanced Theme Preview Styling for Each Theme
// DDLC Theme Preview - Enhanced with animations
.ddlc-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #ff69b4, #a855f7);
      color: white;
      text-shadow: 0 0 10px rgba(255, 105, 180, 0.8);
      animation: ddlc-glow 3s ease-in-out infinite alternate;
    }

    .preview-controls span {
      background: rgba(255, 255, 255, 0.8);
      color: #333;
    }

    .preview-navbar {
      background: rgba(255, 105, 180, 0.2);
      color: white;

      .preview-nav-item.active {
        background: rgba(255, 105, 180, 0.4);
        text-shadow: 0 0 5px rgba(255, 105, 180, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(255, 105, 180, 0.15);
      color: white;
    }

    .preview-main {
      background: rgba(168, 85, 247, 0.1);
      color: white;
    }

    .preview-mod-entry {
      color: rgba(255, 255, 255, 0.8);

      &.active {
        background: rgba(255, 105, 180, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(255, 105, 180, 0.6);
      }

      &:hover {
        background: rgba(255, 105, 180, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #ff69b4, rgba(255, 105, 180, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #a855f7, rgba(168, 85, 247, 0.8));
        color: white;
      }
    }
  }

  .ddlc-heart {
    animation: heartbeat 2s ease-in-out infinite;
    color: #ff69b4;
  }
}

// Classic Theme Preview - Simple and clean
.classic-theme-preview {
  .theme-preview {
    background: #111;

    .preview-titlebar {
      background: #333;
      color: #ddd;
    }

    .preview-controls span {
      background: #666;
      color: #ddd;
    }

    .preview-navbar {
      background: transparent;
      color: #aaa;

      .preview-nav-item.active {
        color: #fff;
        text-shadow: 0 0 10px #fff;
      }
    }

    .preview-sidebar {
      background: transparent;
      color: #ddd;
    }

    .preview-main {
      background: transparent;
      color: #ddd;
    }

    .preview-mod-entry {
      color: #aaa;

      &.active {
        background: linear-gradient(315deg, rgba(187, 85, 153, 0.5), rgba(73, 50, 64, 0.5));
        color: #fff;
      }

      &:hover {
        color: #fff;
      }
    }

    .preview-button {
      &.primary {
        background: #bb5599;
        color: white;
      }

      &.secondary {
        background: #2b2b2b;
        color: #ddd;
      }
    }
  }
}

// Character Theme Previews with Life and Animations

// Monika Theme Preview - Green with code effects
.monika-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(10, 26, 10, 0.95), rgba(20, 40, 20, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #00d166, #00a855);
      color: #e0ffe0;
      text-shadow: 0 0 10px rgba(0, 209, 102, 0.8);
      animation: monika-glitch 4s ease-in-out infinite;
    }

    .preview-controls span {
      background: rgba(0, 209, 102, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(0, 209, 102, 0.2);
      color: #e0ffe0;

      .preview-nav-item.active {
        background: rgba(0, 209, 102, 0.4);
        text-shadow: 0 0 5px rgba(0, 209, 102, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(0, 209, 102, 0.15);
      color: #e0ffe0;
    }

    .preview-main {
      background: rgba(0, 168, 85, 0.1);
      color: #e0ffe0;
    }

    .preview-mod-entry {
      color: rgba(224, 255, 224, 0.8);

      &.active {
        background: rgba(0, 209, 102, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(0, 209, 102, 0.6);
      }

      &:hover {
        background: rgba(0, 209, 102, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #00d166, rgba(0, 209, 102, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #00a855, rgba(0, 168, 85, 0.8));
        color: white;
      }
    }
  }

  .monika-icon {
    animation: code-pulse 2s ease-in-out infinite;
    color: #00d166;
  }
}

// Sayori Theme Preview - Blue with cheerful effects
.sayori-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(10, 26, 42, 0.95), rgba(20, 40, 60, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #22bbff, #1a9bff);
      color: #e0f0ff;
      text-shadow: 0 0 10px rgba(34, 187, 255, 0.8);
      animation: sayori-bounce 3s ease-in-out infinite;
    }

    .preview-controls span {
      background: rgba(34, 187, 255, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(34, 187, 255, 0.2);
      color: #e0f0ff;

      .preview-nav-item.active {
        background: rgba(34, 187, 255, 0.4);
        text-shadow: 0 0 5px rgba(34, 187, 255, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(34, 187, 255, 0.15);
      color: #e0f0ff;
    }

    .preview-main {
      background: rgba(26, 155, 255, 0.1);
      color: #e0f0ff;
    }

    .preview-mod-entry {
      color: rgba(224, 240, 255, 0.8);

      &.active {
        background: rgba(34, 187, 255, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(34, 187, 255, 0.6);
      }

      &:hover {
        background: rgba(34, 187, 255, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #22bbff, rgba(34, 187, 255, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #1a9bff, rgba(26, 155, 255, 0.8));
        color: white;
      }
    }
  }

  .sayori-icon {
    animation: sunshine-glow 2.5s ease-in-out infinite alternate;
    color: #22bbff;
  }
}

// Natsuki Theme Preview - Pink with cute effects
.natsuki-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(42, 10, 26, 0.95), rgba(60, 20, 40, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #ff69b4, #ff4da6);
      color: #ffe0f0;
      text-shadow: 0 0 10px rgba(255, 105, 180, 0.8);
      animation: natsuki-wiggle 2s ease-in-out infinite;
    }

    .preview-controls span {
      background: rgba(255, 105, 180, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(255, 105, 180, 0.2);
      color: #ffe0f0;

      .preview-nav-item.active {
        background: rgba(255, 105, 180, 0.4);
        text-shadow: 0 0 5px rgba(255, 105, 180, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(255, 105, 180, 0.15);
      color: #ffe0f0;
    }

    .preview-main {
      background: rgba(255, 77, 166, 0.1);
      color: #ffe0f0;
    }

    .preview-mod-entry {
      color: rgba(255, 224, 240, 0.8);

      &.active {
        background: rgba(255, 105, 180, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(255, 105, 180, 0.6);
      }

      &:hover {
        background: rgba(255, 105, 180, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #ff69b4, rgba(255, 105, 180, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #ff4da6, rgba(255, 77, 166, 0.8));
        color: white;
      }
    }
  }

  .natsuki-icon {
    animation: kawaii-bounce 1.5s ease-in-out infinite;
    color: #ff69b4;
  }
}

// Yuri Theme Preview - Purple with elegant effects
.yuri-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(26, 10, 42, 0.95), rgba(40, 20, 60, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #a855f7, #9333ea);
      color: #f0e0ff;
      text-shadow: 0 0 10px rgba(168, 85, 247, 0.8);
      animation: yuri-fade 4s ease-in-out infinite alternate;
    }

    .preview-controls span {
      background: rgba(168, 85, 247, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(168, 85, 247, 0.2);
      color: #f0e0ff;

      .preview-nav-item.active {
        background: rgba(168, 85, 247, 0.4);
        text-shadow: 0 0 5px rgba(168, 85, 247, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(168, 85, 247, 0.15);
      color: #f0e0ff;
    }

    .preview-main {
      background: rgba(147, 51, 234, 0.1);
      color: #f0e0ff;
    }

    .preview-mod-entry {
      color: rgba(240, 224, 255, 0.8);

      &.active {
        background: rgba(168, 85, 247, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(168, 85, 247, 0.6);
      }

      &:hover {
        background: rgba(168, 85, 247, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #a855f7, rgba(168, 85, 247, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #9333ea, rgba(147, 51, 234, 0.8));
        color: white;
      }
    }
  }

  .yuri-icon {
    animation: elegant-float 3s ease-in-out infinite alternate;
    color: #a855f7;
  }
}

// Cyberpunk Theme Preview - Neon with glitch effects
.cyberpunk-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(5, 5, 15, 0.95), rgba(10, 10, 25, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #00ffff, #ff00ff);
      color: #e0ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
      animation: cyberpunk-glitch 2s ease-in-out infinite;
    }

    .preview-controls span {
      background: rgba(0, 255, 255, 0.8);
      color: black;
    }

    .preview-navbar {
      background: rgba(0, 255, 255, 0.2);
      color: #e0ffff;

      .preview-nav-item.active {
        background: rgba(255, 0, 255, 0.4);
        text-shadow: 0 0 5px rgba(255, 0, 255, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(0, 255, 255, 0.15);
      color: #e0ffff;
    }

    .preview-main {
      background: rgba(255, 0, 255, 0.1);
      color: #e0ffff;
    }

    .preview-mod-entry {
      color: rgba(224, 255, 255, 0.8);

      &.active {
        background: rgba(255, 0, 255, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(255, 0, 255, 0.6);
      }

      &:hover {
        background: rgba(0, 255, 255, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #00ffff, rgba(0, 255, 255, 0.8));
        color: black;
      }

      &.secondary {
        background: linear-gradient(135deg, #ff00ff, rgba(255, 0, 255, 0.8));
        color: white;
      }
    }
  }

  .cyberpunk-icon {
    animation: neon-pulse 1s ease-in-out infinite alternate;
    color: #00ffff;
  }
}

// Retro Theme Preview - 80s with synthwave effects
.retro-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(25, 10, 5, 0.95), rgba(40, 20, 10, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #ff6b35, #f7931e);
      color: #fff0e0;
      text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
      animation: retro-wave 3s ease-in-out infinite;
    }

    .preview-controls span {
      background: rgba(255, 107, 53, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(255, 107, 53, 0.2);
      color: #fff0e0;

      .preview-nav-item.active {
        background: rgba(247, 147, 30, 0.4);
        text-shadow: 0 0 5px rgba(247, 147, 30, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(255, 107, 53, 0.15);
      color: #fff0e0;
    }

    .preview-main {
      background: rgba(247, 147, 30, 0.1);
      color: #fff0e0;
    }

    .preview-mod-entry {
      color: rgba(255, 240, 224, 0.8);

      &.active {
        background: rgba(255, 107, 53, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(255, 107, 53, 0.6);
      }

      &:hover {
        background: rgba(247, 147, 30, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #ff6b35, rgba(255, 107, 53, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #f7931e, rgba(247, 147, 30, 0.8));
        color: white;
      }
    }
  }

  .retro-icon {
    animation: synthwave-glow 2s ease-in-out infinite alternate;
    color: #ff6b35;
  }
}

// Midnight Theme Preview - Dark blue with peaceful effects
.midnight-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(5, 15, 30, 0.95), rgba(10, 25, 45, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #1e3a8a, #3b82f6);
      color: #e0f0ff;
      text-shadow: 0 0 10px rgba(30, 58, 138, 0.8);
      animation: midnight-drift 4s ease-in-out infinite alternate;
    }

    .preview-controls span {
      background: rgba(30, 58, 138, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(30, 58, 138, 0.2);
      color: #e0f0ff;

      .preview-nav-item.active {
        background: rgba(59, 130, 246, 0.4);
        text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(30, 58, 138, 0.15);
      color: #e0f0ff;
    }

    .preview-main {
      background: rgba(59, 130, 246, 0.1);
      color: #e0f0ff;
    }

    .preview-mod-entry {
      color: rgba(224, 240, 255, 0.8);

      &.active {
        background: rgba(30, 58, 138, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(30, 58, 138, 0.6);
      }

      &:hover {
        background: rgba(59, 130, 246, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #1e3a8a, rgba(30, 58, 138, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #3b82f6, rgba(59, 130, 246, 0.8));
        color: white;
      }
    }
  }

  .midnight-icon {
    animation: moon-glow 3s ease-in-out infinite alternate;
    color: #1e3a8a;
  }
}

// Sunset Theme Preview - Warm with cozy effects
.sunset-theme-preview {
  .theme-preview {
    background: linear-gradient(145deg, rgba(30, 15, 5, 0.95), rgba(45, 25, 10, 0.9));

    .preview-titlebar {
      background: linear-gradient(90deg, #f97316, #dc2626);
      color: #fff5e0;
      text-shadow: 0 0 10px rgba(249, 115, 22, 0.8);
      animation: sunset-glow 3.5s ease-in-out infinite alternate;
    }

    .preview-controls span {
      background: rgba(249, 115, 22, 0.8);
      color: white;
    }

    .preview-navbar {
      background: rgba(249, 115, 22, 0.2);
      color: #fff5e0;

      .preview-nav-item.active {
        background: rgba(220, 38, 38, 0.4);
        text-shadow: 0 0 5px rgba(220, 38, 38, 0.8);
      }
    }

    .preview-sidebar {
      background: rgba(249, 115, 22, 0.15);
      color: #fff5e0;
    }

    .preview-main {
      background: rgba(220, 38, 38, 0.1);
      color: #fff5e0;
    }

    .preview-mod-entry {
      color: rgba(255, 245, 224, 0.8);

      &.active {
        background: rgba(249, 115, 22, 0.3);
        color: white;
        text-shadow: 0 0 5px rgba(249, 115, 22, 0.6);
      }

      &:hover {
        background: rgba(220, 38, 38, 0.2);
        color: white;
      }
    }

    .preview-button {
      &.primary {
        background: linear-gradient(135deg, #f97316, rgba(249, 115, 22, 0.8));
        color: white;
      }

      &.secondary {
        background: linear-gradient(135deg, #dc2626, rgba(220, 38, 38, 0.8));
        color: white;
      }
    }
  }

  .sunset-icon {
    animation: warm-pulse 2.5s ease-in-out infinite alternate;
    color: #f97316;
  }
}

// Theme Preview Animations - Adding Life to Each Theme
@keyframes ddlc-glow {
  0% { text-shadow: 0 0 10px rgba(255, 105, 180, 0.8); }
  100% { text-shadow: 0 0 20px rgba(255, 105, 180, 1), 0 0 30px rgba(168, 85, 247, 0.5); }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes monika-glitch {
  0%, 90% { transform: translateX(0); }
  92% { transform: translateX(-2px); }
  94% { transform: translateX(2px); }
  96% { transform: translateX(-1px); }
  98% { transform: translateX(1px); }
  100% { transform: translateX(0); }
}

@keyframes code-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes sayori-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

@keyframes sunshine-glow {
  0% { text-shadow: 0 0 5px rgba(34, 187, 255, 0.8); }
  100% { text-shadow: 0 0 15px rgba(34, 187, 255, 1), 0 0 25px rgba(255, 255, 0, 0.3); }
}

@keyframes natsuki-wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(1deg); }
  75% { transform: rotate(-1deg); }
}

@keyframes kawaii-bounce {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.05) rotate(2deg); }
}

@keyframes yuri-fade {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

@keyframes elegant-float {
  0% { transform: translateY(0); }
  100% { transform: translateY(-2px); }
}

@keyframes cyberpunk-glitch {
  0%, 85% { transform: translateX(0); filter: hue-rotate(0deg); }
  87% { transform: translateX(-3px); filter: hue-rotate(90deg); }
  89% { transform: translateX(3px); filter: hue-rotate(180deg); }
  91% { transform: translateX(-2px); filter: hue-rotate(270deg); }
  93% { transform: translateX(2px); filter: hue-rotate(360deg); }
  100% { transform: translateX(0); filter: hue-rotate(0deg); }
}

@keyframes neon-pulse {
  0% { text-shadow: 0 0 5px rgba(0, 255, 255, 0.8); }
  100% { text-shadow: 0 0 15px rgba(0, 255, 255, 1), 0 0 25px rgba(255, 0, 255, 0.5); }
}

@keyframes retro-wave {
  0%, 100% { transform: translateY(0) scaleX(1); }
  50% { transform: translateY(-1px) scaleX(1.02); }
}

@keyframes synthwave-glow {
  0% { text-shadow: 0 0 5px rgba(255, 107, 53, 0.8); }
  100% { text-shadow: 0 0 15px rgba(255, 107, 53, 1), 0 0 25px rgba(247, 147, 30, 0.5); }
}

@keyframes midnight-drift {
  0% { transform: translateX(0); }
  100% { transform: translateX(1px); }
}

@keyframes moon-glow {
  0% { text-shadow: 0 0 5px rgba(30, 58, 138, 0.8); }
  100% { text-shadow: 0 0 15px rgba(30, 58, 138, 1), 0 0 25px rgba(59, 130, 246, 0.3); }
}

@keyframes sunset-glow {
  0% { text-shadow: 0 0 10px rgba(249, 115, 22, 0.8); }
  100% { text-shadow: 0 0 20px rgba(249, 115, 22, 1), 0 0 30px rgba(220, 38, 38, 0.5); }
}

@keyframes warm-pulse {
  0% { text-shadow: 0 0 5px rgba(249, 115, 22, 0.8); }
  100% { text-shadow: 0 0 15px rgba(249, 115, 22, 1), 0 0 25px rgba(255, 165, 0, 0.3); }
}

// Classic Theme Overrides - Exact match to proudust/Mod-Manager original styling
body.classic-theme {
  // Override main background to match original
  background-color: #111 !important;
  color: #ddd;

  // Remove all glass effects and modern styling
  .mod-viewer-mod-list,
  .mod-viewer-mod-display,
  .text-container {
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
    backdrop-filter: none;
    padding: 0 1.5em;
  }

  .mod-viewer-mod-list::before,
  .mod-viewer-mod-display::before,
  .text-container::before {
    display: none;
  }

  .mod-view-mod-list-entry {
    background: transparent;
    border: none;
    border-radius: 3px;
    backdrop-filter: none;
    transition: color 0.25s;
    padding: 0.25em 0.5em;
  }

  .mod-view-mod-list-entry::before {
    display: none;
  }

  .mod-view-mod-list-entry:hover {
    background: transparent;
    transform: none;
    box-shadow: none;
    color: #fff;
  }

  .mod-view-mod-list-entry.active {
    background-image: linear-gradient(315deg, rgba(187, 85, 153, 0.5), rgba(73, 50, 64, 0.5));
    box-shadow: none;
    transform: none;
    color: #fff;
  }

  .mod-view-mod-list-title {
    color: #aaa;
    text-shadow: none;
    padding: 0.25em 0.25em 0;
    font-weight: bold;
    text-transform: uppercase;
    border-bottom: none;
  }

  .mod-view-mod-list-title::after {
    display: none;
  }

  // Buttons exactly like original proudust DDMM
  button {
    font-family: "Open Sans", sans-serif;
    color: #fff;
    user-select: none;
    display: inline-block;
    text-decoration: none;
    padding: 0.75em 2em;
    font-size: 14px;
    border-radius: 3px;
    text-align: center;
    font-weight: normal;
    outline: none;
    transition: background-color 0.25s;
    border: none;
    background-color: #bb5599;
    box-shadow: none;
    text-shadow: none;
    transform: none;
  }

  button::before {
    display: none;
  }

  button:hover {
    background-color: #9e3f7e;
    transform: none;
    box-shadow: none;
  }

  button.huge {
    font-size: 1.25em;
  }

  button.primary {
    background-color: #bb5599;
  }

  button.primary:hover {
    background-color: #9e3f7e;
  }

  button.secondary {
    background-color: #2b2b2b;
    color: #ddd;
  }

  button.secondary:hover {
    background-color: #1e1e1e;
  }

  button.dark {
    background-color: #1e1e1e;
  }

  button.dark:hover {
    background-color: #050505;
  }

  button.danger {
    background-color: #eb4d4b;
  }

  button.danger:hover {
    background-color: #e6201d;
  }

  button.warning {
    background-color: #f0932b;
  }

  button.warning:hover {
    background-color: #d97a0f;
  }

  button.success {
    background-color: #6ab04c;
  }

  button.success:hover {
    background-color: #558c3d;
  }

  button[disabled], button[disabled]:hover {
    background-color: #aaa;
    color: #ddd;
  }

  // Form inputs exactly like original
  input[type="text"], input[type="number"], input[type="url"], textarea, select {
    font-family: "Open Sans", sans-serif;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid #1e1e1e;
    color: #ddd;
    font-weight: normal;
    min-width: 350px;
    padding: 0.5em 1em;
    font-size: 1em;
    width: 100%;
    outline: none;
    transition: border-color 0.25s;
    box-shadow: none;
    backdrop-filter: none;
  }

  input.small {
    font-size: 0.75em;
    min-width: 0;
  }

  input[type="text"]:focus, input[type="number"]:focus, input[type="url"]:focus, textarea:focus, select:focus {
    border-color: #aaa;
    box-shadow: none;
    transform: none;
    background-color: rgba(0, 0, 0, 0.5);
  }

  // Reset checkboxes and radios to browser defaults
  input[type="checkbox"], input[type="radio"] {
    appearance: auto;
    width: auto;
    height: auto;
    border-radius: 0;
    background: initial;
    border: initial;
    backdrop-filter: none;
    min-width: auto;
    box-shadow: none;
  }

  input[type="checkbox"]:checked::before,
  input[type="radio"]:checked::before {
    display: none;
  }

  // Remove all modern card styling
  .ddlc-mod-card {
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .ddlc-mod-card::before {
    display: none;
  }

  .ddlc-mod-card:hover {
    transform: none;
    box-shadow: none;
  }

  .ddlc-mod-card h3 {
    color: #ddd;
    text-shadow: none;
  }

  .ddlc-mod-card p {
    color: #ddd;
  }

  // Remove all container styling
  .ddlc-options-container,
  .ddlc-about-container {
    .mod-viewer-mod-list,
    .mod-viewer-mod-display,
    .text-container {
      background: transparent;
      border: none;
    }
  }

  // Simple headings like original
  h1, h2, h3, h4 {
    margin: 0;
    font-weight: 500;
    color: #ddd;
    text-shadow: none;
  }

  h1 {
    color: #fff;
  }

  h2 {
    color: #ddd;
  }

  // Remove theme selector styling
  .theme-option {
    background: transparent;
    border: none;
    border-radius: 0;
    backdrop-filter: none;
  }

  .theme-option:hover {
    transform: none;
    background: transparent;
    box-shadow: none;
  }

  .theme-option.active {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .feature {
    background: transparent;
    border: none;
  }

  // Simple navbar like original
  .navbar {
    color: #fff;
    display: flex;
    flex-shrink: 0;
    background: transparent !important;
    border: none !important;
    backdrop-filter: none !important;
  }

  .navbar::before {
    display: none;
  }

  .nav-links div {
    color: #aaa;
    transition: color 0.25s, text-shadow 0.25s;
  }

  .nav-links div:hover {
    color: #fff;
  }

  .nav-links div.active {
    color: #fff;
    text-shadow: 0 0 10px #fff;
  }

  // Simple progress bars like original
  .progress .bar {
    background-image: linear-gradient(315deg, rgba(187, 85, 153, 0.5), rgba(73, 50, 64, 0.5)) !important;
  }

  // Original scrollbars
  ::-webkit-scrollbar {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 3px;
  }

  // Simple screenshots like original
  .screenshots {
    max-width: 100%;
    overflow-x: scroll;
    display: flex;
  }

  .screenshots img {
    flex-shrink: 0;
    flex-grow: 0;
    display: block;
    width: 192px;
    height: 108px;
    padding: 0.5em;
    cursor: pointer;
    -webkit-user-drag: none;
    border: none;
    border-radius: 0;
  }

  .screenshots img:hover {
    border: none;
  }
}

// Edit Instance Page Styles
.edit-instance-container {
  padding: 2em;
  max-height: 100%;
  overflow-y: auto;
}

.edit-header {
  display: flex;
  align-items: center;
  gap: 1em;
  margin-bottom: 2em;
  padding-bottom: 1em;
  border-bottom: 2px solid rgba(255, 105, 180, 0.3);
}

.back-button {
  min-width: auto;
  padding: 0.6em 1.2em;
  font-size: 0.9em;
}

.edit-header h1 {
  margin: 0;
  flex: 1;
}

.edit-content {
  display: flex;
  flex-direction: column;
  gap: 1.5em;
}

.mod-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1em;
  margin-top: 1em;
}

.mod-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.3em;
}

.mod-info-item strong {
  color: $ddlc-natsuki;
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mod-info-item span {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.5em;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1em;
  margin-top: 1em;
}

.action-buttons button {
  padding: 1em 1.5em;
  font-size: 1em;
  min-width: auto;
}

.form-group {
  margin-bottom: 1.5em;
}

.form-group label {
  display: block;
  margin-bottom: 0.5em;
  color: $ui-text-colour;
  font-weight: 600;
}

.form-group small {
  display: block;
  margin-top: 0.3em;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85em;
  font-style: italic;
}

.form-group input[type="checkbox"] {
  margin-right: 0.5em;
}

// Responsive design for edit page
@media (max-width: 768px) {
  .edit-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5em;
  }

  .mod-info-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}

// New Theme Styles

// Monika Theme - Green focused
body.monika-theme {
  --primary-color: #{$ddlc-monika};
  --secondary-color: #00a855;
  --accent-color: #00ff7f;
  --bg-color: #0a1a0a;
  --surface-color: rgba(0, 209, 102, 0.1);
  --text-color: #e0ffe0;
  --border-color: rgba(0, 209, 102, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);

  .text-container {
    background: linear-gradient(145deg, rgba(10, 26, 10, 0.95), rgba(20, 40, 20, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(0, 209, 102, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(0, 209, 102, 0.2), rgba(0, 168, 85, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(0, 209, 102, 0.3);
  }
}

// Sayori Theme - Blue focused
body.sayori-theme {
  --primary-color: #{$ddlc-sayori};
  --secondary-color: #1a9bff;
  --accent-color: #66d9ff;
  --bg-color: #0a1a2a;
  --surface-color: rgba(34, 187, 255, 0.1);
  --text-color: #e0f0ff;
  --border-color: rgba(34, 187, 255, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);

  .text-container {
    background: linear-gradient(145deg, rgba(10, 26, 42, 0.95), rgba(20, 40, 60, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(34, 187, 255, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(34, 187, 255, 0.2), rgba(26, 155, 255, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(34, 187, 255, 0.3);
  }
}

// Natsuki Theme - Pink focused
body.natsuki-theme {
  --primary-color: #{$ddlc-natsuki};
  --secondary-color: #ff4da6;
  --accent-color: #ff99cc;
  --bg-color: #2a0a1a;
  --surface-color: rgba(255, 105, 180, 0.1);
  --text-color: #ffe0f0;
  --border-color: rgba(255, 105, 180, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);

  .text-container {
    background: linear-gradient(145deg, rgba(42, 10, 26, 0.95), rgba(60, 20, 40, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(255, 105, 180, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(255, 105, 180, 0.2), rgba(255, 77, 166, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(255, 105, 180, 0.3);
  }
}

// Yuri Theme - Purple focused
body.yuri-theme {
  --primary-color: #{$ddlc-yuri};
  --secondary-color: #9333ea;
  --accent-color: #c084fc;
  --bg-color: #1a0a2a;
  --surface-color: rgba(168, 85, 247, 0.1);
  --text-color: #f0e0ff;
  --border-color: rgba(168, 85, 247, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);

  .text-container {
    background: linear-gradient(145deg, rgba(26, 10, 42, 0.95), rgba(40, 20, 60, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(168, 85, 247, 0.2), rgba(147, 51, 234, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.3);
  }
}

// Cyberpunk Theme - Neon focused
body.cyberpunk-theme {
  --primary-color: #00ffff;
  --secondary-color: #ff00ff;
  --accent-color: #ffff00;
  --bg-color: #0a0a0a;
  --surface-color: rgba(0, 255, 255, 0.1);
  --text-color: #00ffff;
  --border-color: rgba(0, 255, 255, 0.5);

  background-color: var(--bg-color);
  color: var(--text-color);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%);

  .text-container {
    background: linear-gradient(145deg, rgba(10, 10, 10, 0.95), rgba(20, 20, 20, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.3), inset 0 0 20px rgba(255, 0, 255, 0.1);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.1));
    border-top: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 15px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
    text-shadow: 0 0 10px currentColor;
  }

  button.primary:hover {
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.4);
  }
}

// Retro Theme - 80s/90s inspired
body.retro-theme {
  --primary-color: #ff6b35;
  --secondary-color: #f7931e;
  --accent-color: #ffcc02;
  --bg-color: #2d1b69;
  --surface-color: rgba(255, 107, 53, 0.1);
  --text-color: #ffeaa7;
  --border-color: rgba(255, 107, 53, 0.4);

  background-color: var(--bg-color);
  color: var(--text-color);
  background-image:
    linear-gradient(45deg, rgba(255, 107, 53, 0.1) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(247, 147, 30, 0.1) 25%, transparent 25%);
  background-size: 40px 40px;

  .text-container {
    background: linear-gradient(145deg, rgba(45, 27, 105, 0.95), rgba(60, 40, 120, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.2), rgba(247, 147, 30, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  }
}

// Midnight Theme - Dark blue/black
body.midnight-theme {
  --primary-color: #1e3a8a;
  --secondary-color: #3b82f6;
  --accent-color: #60a5fa;
  --bg-color: #0f172a;
  --surface-color: rgba(30, 58, 138, 0.1);
  --text-color: #e2e8f0;
  --border-color: rgba(30, 58, 138, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);

  .text-container {
    background: linear-gradient(145deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(30, 58, 138, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(30, 58, 138, 0.3);
  }
}

// Sunset Theme - Orange/red gradient
body.sunset-theme {
  --primary-color: #f97316;
  --secondary-color: #dc2626;
  --accent-color: #fbbf24;
  --bg-color: #451a03;
  --surface-color: rgba(249, 115, 22, 0.1);
  --text-color: #fed7aa;
  --border-color: rgba(249, 115, 22, 0.3);

  background-color: var(--bg-color);
  color: var(--text-color);
  background-image:
    radial-gradient(circle at 50% 100%, rgba(249, 115, 22, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(220, 38, 38, 0.1) 0%, transparent 50%);

  .text-container {
    background: linear-gradient(145deg, rgba(69, 26, 3, 0.95), rgba(92, 40, 10, 0.9));
    border: 2px solid var(--border-color);
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.2);
  }

  .navbar {
    background: linear-gradient(90deg, rgba(249, 115, 22, 0.2), rgba(220, 38, 38, 0.1));
    border-top: 2px solid var(--border-color);
  }

  .nav-links div.active {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
  }

  button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--border-color);
  }

  button.primary:hover {
    box-shadow: 0 4px 16px rgba(249, 115, 22, 0.3);
  }
}

// endregion